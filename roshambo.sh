#!/bin/bash

# =============================================================================
# Roshambo Environment Setup Script for WSL
# =============================================================================
# This script sets up the environment variables for Roshambo with RDKit in WSL
# Usage: source roshambo.sh
# =============================================================================

echo "Setting up Roshambo environment for WSL..."

# =============================================================================
# Conda Environment Configuration for WSL
# =============================================================================
echo "Activating conda environment..."

# Activate the roshambo conda environment (WSL path)
conda activate /mnt/d/conda_envs/roshambo

# Verify conda environment activation
if [ "$CONDA_DEFAULT_ENV" != "roshambo" ] && [ "$CONDA_DEFAULT_ENV" != "/mnt/d/conda_envs/roshambo" ]; then
    echo "Warning: Conda environment may not be properly activated"
    echo "Current environment: $CONDA_DEFAULT_ENV"
fi

# =============================================================================
# RDKit Configuration for WSL
# =============================================================================
echo "Setting up RDKit environment variables..."

# Navigate to RDKit directory and set RDBASE
cd /mnt/d/roshambo/rdkit
export RDBASE=$(pwd)
export RDKIT_LIB_DIR="$RDBASE/lib"
export RDKIT_INCLUDE_DIR="$RDBASE/Code"
export RDKIT_DATA_DIR="$RDBASE/Data"

# =============================================================================
# Python Path Configuration (WSL specific)
# =============================================================================
# Set PYTHONPATH to include RDKit
export PYTHONPATH="$PYTHONPATH:$RDBASE"

# =============================================================================
# Library Path Configuration for WSL
# =============================================================================
# Set library paths for WSL environment
export LD_LIBRARY_PATH="$RDBASE/lib:$CONDA_PREFIX/lib:$LD_LIBRARY_PATH"

# =============================================================================
# CUDA Configuration for WSL
# =============================================================================
export CUDA_HOME="/usr/local/cuda"
export CUDA_ROOT="/usr/local/cuda"
export PATH="$CUDA_HOME/bin:$PATH"

# =============================================================================
# Roshambo Specific Configuration
# =============================================================================
export ROSHAMBO_ROOT="/mnt/d/roshambo"
export ROSHAMBO_DATA_DIR="$ROSHAMBO_ROOT/data"

# =============================================================================
# GPU Configuration
# =============================================================================
export CUDA_VISIBLE_DEVICES="0"  # Use GPU 0 by default
export NVIDIA_VISIBLE_DEVICES="0"

# Navigate back to the main roshambo directory
cd /mnt/d/roshambo

# =============================================================================
# Environment Verification
# =============================================================================
echo ""
echo "Environment variables set:"
echo "  CONDA_PREFIX: $CONDA_PREFIX"
echo "  CONDA_DEFAULT_ENV: $CONDA_DEFAULT_ENV"
echo "  RDBASE: $RDBASE"
echo "  PYTHONPATH: $PYTHONPATH"
echo "  LD_LIBRARY_PATH: $LD_LIBRARY_PATH"
echo "  CUDA_HOME: $CUDA_HOME"
echo "  ROSHAMBO_ROOT: $ROSHAMBO_ROOT"
echo "  Current directory: $(pwd)"

# =============================================================================
# Package Import Testing
# =============================================================================
echo ""
echo "Testing RDKit import..."
python -c "
try:
    from rdkit import Chem
    from rdkit.Chem import AllChem
    print('✓ RDKit imported successfully')
    print('  RDKit version:', Chem.rdBase.rdkitVersion)
except ImportError as e:
    print('✗ RDKit import failed:', e)
    print('  Check RDBASE and PYTHONPATH settings')
"

echo ""
echo "Testing Roshambo import..."
python -c "
try:
    import roshambo
    from roshambo.api import get_similarity_scores
    print('✓ Roshambo imported successfully')
except ImportError as e:
    print('✗ Roshambo import failed:', e)
    print('  Make sure Roshambo is installed in the conda environment')
"

# =============================================================================
# CUDA Testing
# =============================================================================
echo ""
echo "Testing CUDA availability..."
python -c "
try:
    import torch
    if torch.cuda.is_available():
        print('✓ CUDA available')
        print('  CUDA version:', torch.version.cuda)
        print('  GPU count:', torch.cuda.device_count())
        for i in range(torch.cuda.device_count()):
            print(f'  GPU {i}: {torch.cuda.get_device_name(i)}')
    else:
        print('⚠ CUDA not available')
except ImportError:
    print('⚠ PyTorch not installed, cannot check CUDA')
except Exception as e:
    print('⚠ CUDA check failed:', e)
"

# =============================================================================
# Boost Library Check (WSL specific issue from memory)
# =============================================================================
echo ""
echo "Checking Boost library configuration..."
python -c "
import os
conda_prefix = os.environ.get('CONDA_PREFIX', '')
if conda_prefix:
    boost_lib = os.path.join(conda_prefix, 'lib')
    boost_include = os.path.join(conda_prefix, 'include')
    print(f'Conda Boost lib path: {boost_lib}')
    print(f'Conda Boost include path: {boost_include}')
    if os.path.exists(boost_lib):
        print('✓ Boost library directory found in conda environment')
    else:
        print('⚠ Boost library directory not found in conda environment')
else:
    print('⚠ CONDA_PREFIX not set')
"

echo ""
echo "==============================================================================="
echo "Roshambo WSL environment setup complete!"
echo "==============================================================================="
echo ""
echo "Usage instructions:"
echo "  1. To activate this environment: source roshambo.sh"
echo "  2. To run Roshambo API tests: python simple_roshambo_test.py"
echo "  3. To run full test suite: bash run_roshambo_test.sh"
echo ""
echo "If you encounter Boost version issues:"
echo "  conda install -c conda-forge boost=1.81.0"
echo ""
echo "Environment is ready for Roshambo operations!"
